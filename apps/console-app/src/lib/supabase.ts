import { createBrowserClient, createServer<PERSON>lient, isBrowser } from '@supabase/ssr'
import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public'
import type { Database } from '../../../../packages/database-types'

// Browser client for client-side operations
export const supabase = createBrowserClient<Database>(
  PUBLIC_SUPABASE_URL,
  PUBLIC_SUPABASE_ANON_KEY
)

// Server client factory for server-side operations
export function createSupabaseServerClient(cookies: {
  get: (name: string) => string | undefined
  set: (name: string, value: string, options?: any) => void
  remove: (name: string, options?: any) => void
}) {
  return createServerClient<Database>(
    PUBLIC_SUPABASE_URL,
    PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          // Get all cookies that start with 'sb-'
          const allCookies = []
          const cookieNames = ['sb-access-token', 'sb-refresh-token', 'sb-user']

          for (const name of cookieNames) {
            const value = cookies.get(name)
            if (value) {
              allCookies.push({ name, value })
            }
          }

          return allCookies
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            cookies.set(name, value, {
              ...options,
              path: '/',
              httpOnly: true,
              secure: process.env.NODE_ENV === 'production',
              sameSite: 'lax'
            })
          })
        }
      }
    }
  )
}

